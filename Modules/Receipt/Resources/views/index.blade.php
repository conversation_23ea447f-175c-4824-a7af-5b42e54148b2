@extends('layouts.app')
@section('title', __('receipt::lang.receipts'))

@section('content')

<!-- Content Header (Page header) -->
<section class="content-header">
    <h1 class="tw-text-xl md:tw-text-3xl tw-font-bold tw-text-black">@lang('receipt::lang.receipts')</h1>
</section>

<!-- Main content -->
<section class="content">
    <div class="row">
        <div class="col-md-12">
            @component('components.filters', ['title' => __('report.filters')])
                @if(auth()->user()->can('all_receipt.access'))
                    <div class="col-md-3">
                        <div class="form-group">
                            {!! Form::label('location_id',  __('purchase.business_location') . ':') !!}
                            {!! Form::select('location_id', $business_locations, null, ['class' => 'form-control select2', 'style' => 'width:100%']); !!}
                        </div>
                    </div>
                @endif
                <div class="col-md-3">
                    <div class="form-group">
                        {!! Form::label('receipt_category_id', __('receipt::lang.receipt_category') . ':') !!}
                        {!! Form::select('receipt_category_id', $receipt_categories, null, ['class' => 'form-control select2', 'style' => 'width:100%', 'placeholder' => __('messages.all')]); !!}
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="form-group">
                        {!! Form::label('receipt_date_range', __('report.date_range') . ':') !!}
                        {!! Form::text('receipt_date_range', null, ['placeholder' => __('lang_v1.select_a_date_range'), 'class' => 'form-control', 'id' => 'receipt_date_range', 'readonly']); !!}
                    </div>
                </div>
            @endcomponent
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            @component('components.widget', ['class' => 'box-primary', 'title' => __('receipt::lang.all_receipts')])
                @can('receipt.create')
                    @slot('tool')
                        <div class="box-tools">
                            <a class="tw-dw-btn tw-bg-gradient-to-r tw-from-indigo-600 tw-to-blue-500 tw-font-bold tw-text-white tw-border-none tw-rounded-full pull-right tw-m-2"
                                href="{{action('Modules\Receipt\Http\Controllers\ReceiptController@create')}}">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                    class="icon icon-tabler icons-tabler-outline icon-tabler-plus">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                    <path d="M12 5l0 14" />
                                    <path d="M5 12l14 0" />
                                </svg> @lang('messages.add')
                            </a>
                        </div>
                    @endslot
                @endcan
                <div class="table-responsive">
                    <table class="table table-bordered table-striped" id="receipt_table">
                        <thead>
                            <tr>
                                <th>@lang('messages.action')</th>
                                <th>@lang('messages.date')</th>
                                <th>@lang('purchase.ref_no')</th>
                                <th>@lang('receipt::lang.receipt_category')</th>
                                <th>@lang('business.location')</th>
                                <th>@lang('sale.payment_status')</th>
                                <th>@lang('sale.total_amount')</th>
                                <th>@lang('purchase.payment_due')</th>
                                <th>@lang('lang_v1.payment_note')</th>
                                <th>@lang('lang_v1.added_by')</th>
                            </tr>
                        </thead>
                        <tfoot>
                            <tr class="bg-gray font-17 text-center footer-total">
                                <td colspan="5"><strong>@lang('sale.total'):</strong></td>
                                <td id="footer_payment_status_count"></td>
                                <td id="footer_receipt_total"></td>
                                <td id="footer_total_due"></td>
                                <td colspan="2"></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            @endcomponent
        </div>
    </div>

</section>
<!-- /.content -->
<div class="modal fade payment_modal" tabindex="-1" role="dialog"
    aria-labelledby="gridSystemModalLabel">
</div>

<div class="modal fade edit_payment_modal" tabindex="-1" role="dialog"
    aria-labelledby="gridSystemModalLabel">
</div>
@stop
@section('javascript')
<script src="{{ asset('js/payment.js?v=' . $asset_v) }}"></script>
<script type="text/javascript">
$(document).ready( function(){
    //Receipt table
    receipt_table = $('#receipt_table').DataTable({
        processing: true,
        serverSide: true,
        aaSorting: [[1, 'desc']],
        "ajax": {
            "url": "{{action('Modules\Receipt\Http\Controllers\ReceiptController@index')}}",
            "data": function ( d ) {
                if($('#location_id').length) {
                    d.location_id = $('#location_id').val();
                }
                d.receipt_category_id = $('#receipt_category_id').val();

                var start = '';
                var end = '';
                if($('#receipt_date_range').val()){
                    start = $('input#receipt_date_range').data('daterangepicker').startDate.format('YYYY-MM-DD');
                    end = $('input#receipt_date_range').data('daterangepicker').endDate.format('YYYY-MM-DD');
                }
                d.start_date = start;
                d.end_date = end;
            }
        },
        columnDefs: [ {
            "targets": [0],
            "orderable": false,
            "searchable": false
        } ],
        columns: [
            { data: 'action', name: 'action'},
            { data: 'transaction_date', name: 'transaction_date'  },
            { data: 'ref_no', name: 'transactions.ref_no'},
            { data: 'receipt_category', name: 'receipt_category'},
            { data: 'location_name', name: 'bl.name'},
            { data: 'payment_status', name: 'transactions.payment_status'},
            { data: 'final_total', name: 'transactions.final_total'},
            { data: 'payment_due', name: 'payment_due', "searchable": false},
            { data: 'payment_note', name: 'tp.note'},
            { data: 'added_by', name: 'u.first_name'}
        ],
        "fnDrawCallback": function (oSettings) {
            var total_receipt = sum_table_col($('#receipt_table'), 'final_total');
            $('#footer_receipt_total').text(total_receipt);

            var total_due = sum_table_col($('#receipt_table'), 'payment_due');
            $('#footer_total_due').text(total_due);

            $('#footer_payment_status_count').html(__sum_status_html($('#receipt_table'), 'payment_status'));
            __currency_convert_recursively($('#receipt_table'));
        },
        createdRow: function( row, data, dataIndex ) {
            $( row ).find('td:eq(5)').addClass('payment_status');
            $( row ).find('td:eq(6)').addClass('final_total');
            $( row ).find('td:eq(7)').addClass('payment_due');
        }
    });

    //Date range as a button
    $('#receipt_date_range').daterangepicker(
        dateRangeSettings,
        function (start, end) {
            $('#receipt_date_range').val(start.format(moment_date_format) + ' ~ ' + end.format(moment_date_format));
            receipt_table.ajax.reload();
        }
    );
    $('#receipt_date_range').on('cancel.daterangepicker', function(ev, picker) {
        $('#receipt_date_range').val('');
        receipt_table.ajax.reload();
    });

    $(document).on('change', '#location_id, #receipt_category_id', function() {
        receipt_table.ajax.reload();
    });

    $(document).on('click', '.delete_receipt_button', function(){
        swal({
          title: LANG.sure,
          text: LANG.confirm_delete_receipt,
          icon: "warning",
          buttons: true,
          dangerMode: true,
        }).then((willDelete) => {
            if (willDelete) {
                var href = $(this).data('href');
                var data = $(this).serialize();

                $.ajax({
                    method: "DELETE",
                    url: href,
                    dataType: "json",
                    data: data,
                    success: function(result){
                        if(result.success == true){
                            toastr.success(result.msg);
                            receipt_table.ajax.reload();
                        } else {
                            toastr.error(result.msg);
                        }
                    }
                });
            }
        });
    });
});
</script>
@endsection
