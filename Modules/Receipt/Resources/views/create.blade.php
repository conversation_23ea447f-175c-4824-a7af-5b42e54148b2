@extends('layouts.app')
@section('title', __('receipt::lang.add_receipt'))

@section('content')

<!-- Content Header (Page header) -->
<section class="content-header">
    <h1 class="tw-text-xl md:tw-text-3xl tw-font-bold tw-text-black">@lang('receipt::lang.add_receipt')</h1>
</section>

<!-- Main content -->
<section class="content">
	{!! Form::open(['url' => action('Modules\Receipt\Http\Controllers\ReceiptController@store'), 'method' => 'post', 'id' => 'add_receipt_form', 'files' => true ]) !!}
	<div class="box box-solid">
		<div class="box-body">
			<div class="row">
				<div class="col-sm-4">
					<div class="form-group">
						{!! Form::label('location_id', __('purchase.business_location').':*') !!}
						{!! Form::select('location_id', $business_locations, null, ['class' => 'form-control select2', 'placeholder' => __('messages.please_select'), 'required']); !!}
					</div>
				</div>
				<div class="col-sm-4">
					<div class="form-group">
						{!! Form::label('receipt_category_id', __('receipt::lang.receipt_category').':') !!}
						{!! Form::select('receipt_category_id', $receipt_categories, null, ['class' => 'form-control select2', 'placeholder' => __('messages.please_select')]); !!}
					</div>
				</div>
				<div class="col-sm-4">
					<div class="form-group">
						{!! Form::label('ref_no', __('purchase.ref_no').':') !!}
						{!! Form::text('ref_no', null, ['class' => 'form-control']); !!}
						<p class="help-block">@lang('lang_v1.leave_empty_to_autogenerate')</p>
					</div>
				</div>

				<div class="col-sm-4">
					<div class="form-group">
						{!! Form::label('payer_type', 'Đối tượng nộp:*') !!}
						{!! Form::select('payer_type', [
							'customer' => 'Khách hàng',
							'supplier' => 'Nhà cung cấp',
							'other' => 'Khác'
						], null, ['class' => 'form-control select2', 'placeholder' => __('messages.please_select'), 'required', 'id' => 'payer_type']); !!}
					</div>
				</div>
				<div class="col-sm-4">
					<div class="form-group">
						{!! Form::label('contact_id', 'Người nộp:*') !!}
						{!! Form::select('contact_id', [], null, ['class' => 'form-control select2', 'placeholder' => __('messages.please_select'), 'required', 'id' => 'contact_id']); !!}
						{!! Form::text('other_contact_name', null, ['class' => 'form-control', 'placeholder' => 'Nhập tên người nộp', 'id' => 'other_contact_name', 'style' => 'display:none;']); !!}
					</div>
				</div>
				<div class="clearfix"></div>
				<div class="col-sm-4">
                    <div class="form-group">
                        {!! Form::label('document', __('purchase.attach_document') . ':') !!}
                        {!! Form::file('document', ['id' => 'upload_document', 'accept' => implode(',', array_keys(config('constants.document_upload_mimes_types')))]); !!}
                        <small><p class="help-block">@lang('purchase.max_file_size', ['size' => (config('constants.document_size_limit') / 1000000)])
                        @includeIf('components.document_help_text')</p></small>
                    </div>
                </div>
			</div>
		</div>
	</div> <!--box end-->
	@component('components.widget', ['class' => 'box-solid', 'id' => "payment_rows_div", 'title' => __('purchase.add_payment')])
	<div class="payment_row">
		@include('sale_pos.partials.payment_row_form', [
			'row_index' => 0,
			'show_date' => true,
			'payment_line' => $payment_line,
			'payment_types' => $payment_types,
			'accounts' => $accounts,
			'readonly' => false,
			'show_denomination' => false,
			'col_class' => 'col-md-6'
		])
		<hr>
		<div class="row">
			<div class="col-sm-12">
				<div class="pull-right">
					<strong>@lang('purchase.payment_due'):</strong>
					<span id="payment_due">{{@num_format(0)}}</span>
				</div>
			</div>
		</div>
	</div>
	@endcomponent
	<div class="col-sm-12 text-center">
		<button type="submit" class="tw-dw-btn tw-dw-btn-primary tw-dw-btn-lg tw-text-white">@lang('messages.save')</button>
	</div>
{!! Form::close() !!}
</section>
@endsection
@section('javascript')
<script type="text/javascript">
	// Contact data from server
	var customers = @json($customers);
	var suppliers = @json($suppliers);
	var both_contacts = @json($both_contacts);

	$(document).ready( function(){
		$('.paid_on').datetimepicker({
            format: moment_date_format + ' ' + moment_time_format,
            ignoreReadonly: true,
        });

        // Handle payer type change
        $('#payer_type').on('change', function() {
            var payer_type = $(this).val();
            var contact_dropdown = $('#contact_id');
            var other_contact_input = $('#other_contact_name');

            if (payer_type === 'other') {
                // Show text input for "Khác"
                contact_dropdown.hide().prop('required', false);
                other_contact_input.show().prop('required', true);
                // Update label
                $('label[for="contact_id"]').text('Người nộp:*');
            } else {
                // Show dropdown for customer/supplier
                other_contact_input.hide().prop('required', false);
                contact_dropdown.show().prop('required', true);

                // Clear current options
                contact_dropdown.empty().append('<option value="">@lang("messages.please_select")</option>');

                var contacts_data = {};
                if (payer_type === 'customer') {
                    contacts_data = customers;
                } else if (payer_type === 'supplier') {
                    contacts_data = suppliers;
                }

                // Populate new options
                $.each(contacts_data, function(key, value) {
                    contact_dropdown.append('<option value="' + key + '">' + value + '</option>');
                });

                // Refresh select2
                contact_dropdown.trigger('change');
            }
        });

        // Auto-fill payment amount when total amount changes
        $(document).on('change', 'input.payment-amount', function() {
            calculateReceiptPaymentDue();
        });
	});

	__page_leave_confirmation('#add_receipt_form');

	function calculateReceiptPaymentDue(){
		var total_paying = 0;
		$('#payment_rows_div').find('.payment-amount').each( function(){
			if(parseFloat($(this).val())) {
				total_paying += __read_number($(this));
			}
		});
		var due = 0 - total_paying; // Since we removed final_total field
		$('#payment_due').text(__currency_trans_from_en(due, true));
	}
</script>
@endsection
